body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 40px); /* 调整最小高度 */
    background-color: #f8f9fa;
    margin: 0;
    padding: 20px 0; /* 调整垂直内边距 */
    box-sizing: border-box;
}
.container {
    background-color: #ffffff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 1200px; /* 调整最大宽度 */
    text-align: center;
    margin-bottom: 30px; /* 增加底部外边距 */
}
h1 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 2.2em;
    font-weight: 600;
}
.input-group {
    margin-bottom: 15px; /* 调整间距 */
    text-align: left;
}
.input-group label {
    display: block;
    color: #495057;
    font-weight: 500;
    margin-bottom: 8px;
}
input[type="text"],
input[type="number"],
select {
    width: calc(100% - 20px);
    padding: 12px 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 1em;
    box-sizing: border-box;
    transition: border-color 0.3s ease;
    /* Debugging styles */
    outline: 2px solid red !important; /* Make prompt input clearly visible */
    background-color: rgba(255, 255, 0, 0.2) !important;
}
input[type="text"]:focus,
input[type="number"]:focus,
select:focus {
    border-color: #007bff;
    outline: none;
}
.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px; /* 调整间距 */
}
.checkbox-group input[type="checkbox"] {
    margin-right: 10px;
    width: auto;
}
.checkbox-group label {
    margin-bottom: 0;
    color: #495057;
}
button {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: 500;
    transition: background-color 0.3s ease, transform 0.2s ease;
}
button:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
}
button:disabled {
    background-color: #adb5bd;
    cursor: not-allowed;
    transform: none;
}
#status {
    margin-top: 15px; /* 调整间距 */
    font-size: 1em;
    color: #6c757d;
}
#error {
    color: #dc3545;
    margin-top: 10px; /* 调整间距 */
    font-weight: 500;
}
#imageContainer {
    margin-top: 25px; /* 调整间距 */
    border: 1px solid #e9ecef;
    padding: 15px;
    border-radius: 8px;
    background-color: #f8f9fa;
}
#generatedImage {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.history-container {
    margin-top: 20px; /* 调整间距 */
    width: 100%;
    max-width: 1200px; /* 调整最大宽度 */
    background-color: #ffffff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}
.history-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 15px; /* 调整间距 */
}
.history-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
    padding: 10px;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    /* Debugging styles */
    outline: 2px solid blue !important; /* Make history items clearly visible */
    background-color: rgba(0, 255, 255, 0.2) !important;
}
.history-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
.history-item img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 5px;
}
.history-item .prompt-preview {
    font-size: 0.8em;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
}
.history-item .prompt-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    font-size: 0.9em;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}
.history-item:hover .prompt-overlay {
    opacity: 1;
}
.clear-history-btn {
    background-color: #dc3545;
    margin-top: 15px; /* 调整间距 */
    padding: 8px 15px;
    font-size: 0.9em;
    font-weight: 500;
}
.clear-history-btn:hover {
    background-color: #c82333;
}
.history-item.loading {
    border: 2px dashed #007bff;
    background-color: #e7f3ff;
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    min-height: 120px; /* Adjust to match history item height */
}
.history-item.loading .loading-spinner {
    display: block;
    text-align: center;
    font-size: 1.2em;
    color: #007bff;
    margin-bottom: 5px;
}
.history-item.loading .prompt-text-loading {
    font-size: 0.9em;
    color: #555;
    text-align: center;
    word-break: break-word;
    white-space: normal;
}
.history-item.loading img,
.history-item.loading .prompt-preview,
.history-item.loading .prompt-overlay {
    display: none;
}
.history-item.error {
    border: 2px solid #dc3545;
    background-color: #ffe6e6;
    color: #dc3545;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    min-height: 120px;
}
.history-item.error .error-message {
    display: block;
    font-weight: bold;
    font-size: 0.9em;
}
.history-item.error img,
.history-item.error .prompt-preview,
.history-item.error .prompt-overlay {
    display: none;
}

/* Modal for image display */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1000; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0,0,0,0.9); /* Black w/ opacity */
    align-items: center;
    justify-content: center;
    pointer-events: none; /* Add this line to prevent clicks when hidden */
}

.modal.active {
    pointer-events: auto; /* Enable clicks when active */
}

.modal-content {
    margin: auto;
    display: block;
    max-width: 90%;
    max-height: 90%;
    border-radius: 12px;
}

.modal-content, #caption {
    animation-name: zoom;
    animation-duration: 0.6s;
}

@keyframes zoom {
    from {transform: scale(0)}
    to {transform: scale(1)}
}

.close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
}

.close:hover,
.close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
}

#caption {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 700px;
    text-align: center;
    color: #ccc;
    padding: 10px 0;
    height: 150px;
} 