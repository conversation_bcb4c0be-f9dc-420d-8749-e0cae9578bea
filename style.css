body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 40px); /* 调整最小高度 */
    background-color: #f8f9fa;
    margin: 0;
    padding: 20px 0; /* 调整垂直内边距 */
    box-sizing: border-box;
}
.container {
    background-color: #ffffff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 1200px; /* 调整最大宽度 */
    text-align: center;
    margin-bottom: 30px; /* 增加底部外边距 */
}
h1 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 2.2em;
    font-weight: 600;
}
.input-group {
    margin-bottom: 15px; /* 调整间距 */
    text-align: left;
}
.input-group label {
    display: block;
    color: #495057;
    font-weight: 500;
    margin-bottom: 8px;
}
input[type="text"],
input[type="number"],
select {
    width: calc(100% - 20px);
    padding: 12px 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 1em;
    box-sizing: border-box;
    transition: border-color 0.3s ease;
}
input[type="text"]:focus,
input[type="number"]:focus,
select:focus {
    border-color: #007bff;
    outline: none;
}
.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px; /* 调整间距 */
}
.checkbox-group input[type="checkbox"] {
    margin-right: 10px;
    width: auto;
}
.checkbox-group label {
    margin-bottom: 0;
    color: #495057;
}
button {
    background-color: #007bff;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}
button:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}
button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}
button:disabled {
    background-color: #adb5bd;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}
#status {
    margin-top: 15px; /* 调整间距 */
    font-size: 1em;
    color: #6c757d;
}
#error {
    color: #dc3545;
    margin-top: 10px; /* 调整间距 */
    font-weight: 500;
}
#imageContainer {
    margin-top: 25px; /* 调整间距 */
    border: 1px solid #e9ecef;
    padding: 15px;
    border-radius: 8px;
    background-color: #f8f9fa;
}
#generatedImage {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.history-container {
    margin-top: 20px; /* 调整间距 */
    width: 100%;
    max-width: 1200px; /* 调整最大宽度 */
    background-color: #ffffff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
}
.history-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 固定4列 */
    gap: 20px;
    margin-top: 15px; /* 调整间距 */
}
.history-item {
    border: none; /* 移除边框 */
    border-radius: 12px;
    overflow: hidden;
    background-color: transparent; /* 移除背景色 */
    padding: 0; /* 移除内边距 */
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    aspect-ratio: 3/4; /* 设置3:4比例 */
}
.history-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}
.history-item img,
.history-item .history-thumb {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
    margin-bottom: 0;
    cursor: pointer;
    transition: opacity 0.2s ease, transform 0.2s ease;
    border: none; /* 确保图片无边框 */
}

.history-item img:hover,
.history-item .history-thumb:hover {
    opacity: 0.9;
    transform: scale(1.02);
}
.history-item .prompt-preview {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    font-size: 0.75em;
    padding: 15px 8px 5px 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

.history-item .image-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 0.65em;
    padding: 5px 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 0 0 12px 12px;
}

.history-item .model-name {
    background: rgba(0, 123, 255, 0.8);
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

.history-item .generation-time {
    background: rgba(40, 167, 69, 0.8);
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}
.history-item .prompt-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    font-size: 0.9em;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none; /* 防止覆盖层阻止点击事件 */
}
.history-item:hover .prompt-overlay {
    opacity: 1;
}
.clear-history-btn {
    background-color: #dc3545;
    margin-top: 15px; /* 调整间距 */
    padding: 8px 15px;
    font-size: 0.9em;
    font-weight: 500;
}
.clear-history-btn:hover {
    background-color: #c82333;
}
.history-item.loading {
    border: 2px dashed #007bff;
    background-color: #e7f3ff;
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border-radius: 12px;
}
.history-item.loading .loading-spinner {
    display: block;
    text-align: center;
    font-size: 1.2em;
    color: #007bff;
    margin-bottom: 5px;
}
.history-item.loading .prompt-text-loading {
    font-size: 0.9em;
    color: #555;
    text-align: center;
    word-break: break-word;
    white-space: normal;
    margin-bottom: 5px;
}

.history-item.loading .model-loading {
    font-size: 0.8em;
    color: #007bff;
    text-align: center;
    background: rgba(0, 123, 255, 0.1);
    padding: 3px 8px;
    border-radius: 10px;
    margin-top: 5px;
}
.history-item.loading img,
.history-item.loading .prompt-preview,
.history-item.loading .prompt-overlay {
    display: none;
}
.history-item.error {
    border: 2px solid #dc3545;
    background-color: #ffe6e6;
    color: #dc3545;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border-radius: 12px;
}
.history-item.error .error-message {
    display: block;
    font-weight: bold;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.history-item.error .model-error {
    font-size: 0.8em;
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
    padding: 2px 6px;
    border-radius: 8px;
    margin-top: 5px;
}
.history-item.error img,
.history-item.error .prompt-preview,
.history-item.error .prompt-overlay {
    display: none;
}

/* Modal for image display */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1000; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    background-color: rgba(0,0,0,0.9); /* Black w/ opacity */
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.modal.active {
    display: flex !important;
}

.modal-content {
    max-width: calc(100vw - 40px);
    max-height: calc(100vh - 40px);
    width: auto;
    height: auto;
    border-radius: 12px;
    object-fit: contain;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    margin: 0;
}

.modal-content, #caption {
    animation-name: zoom;
    animation-duration: 0.6s;
}

@keyframes zoom {
    from {transform: scale(0)}
    to {transform: scale(1)}
}

.close {
    position: fixed;
    top: 20px;
    right: 30px;
    color: #fff;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
    cursor: pointer;
    z-index: 1001;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.close:hover,
.close:focus {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.2);
    text-decoration: none;
    transform: scale(1.1);
}

#caption {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    max-width: 700px;
    text-align: center;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.8);
    padding: 15px 20px;
    border-radius: 12px;
    font-size: 16px;
    line-height: 1.4;
    word-wrap: break-word;
}

.modal-prompt {
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: 500;
}

.modal-info {
    display: flex;
    justify-content: center;
    gap: 15px;
    font-size: 14px;
    opacity: 0.9;
}

.modal-model,
.modal-time {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 10px;
    border-radius: 15px;
    font-weight: 500;
}

.modal-model {
    background: rgba(0, 123, 255, 0.6);
}

.modal-time {
    background: rgba(40, 167, 69, 0.6);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .history-grid {
        grid-template-columns: repeat(2, 1fr); /* 小屏幕显示2列 */
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .history-grid {
        grid-template-columns: repeat(1, 1fr); /* 超小屏幕显示1列 */
        gap: 10px;
    }

    .history-container {
        padding: 15px;
    }
}