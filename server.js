require('dotenv').config();
const express = require('express');
const fetch = require('node-fetch');
const path = require('path');

const app = express();
const port = 3000;

// Enable CORS for development
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*'); // Adjust this for production
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    next();
});

app.use(express.json()); // For parsing application/json
app.use(express.static(path.join(__dirname))); // Serve static files (like index.html)

// 读取 API Key
const POLLINATIONS_API_KEY = process.env.POLLINATIONS_API_KEY; 

// Route to handle image generation requests from the frontend
app.post('/generate-image', async (req, res) => {
    const { prompt, model, seed, width, height, nologo, isPrivate, enhance, safe, transparent } = req.body;

    if (!prompt) {
        return res.status(400).json({ error: 'Prompt is required.' });
    }

    try {
        let imageUrl = `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}`;
        const params = new URLSearchParams();

        if (model && model !== 'flux') params.append('model', model);
        if (seed) params.append('seed', seed);
        if (width && width !== '1024') params.append('width', width);
        if (height && height !== '1024') params.append('height', height);
        if (nologo) params.append('nologo', 'true');
        if (isPrivate) params.append('private', 'true');
        if (enhance) params.append('enhance', 'true');
        if (safe) params.append('safe', 'true');
        if (transparent) params.append('transparent', 'true');

        if (params.toString()) {
            imageUrl += `?${params.toString()}`;
        }

        console.log(`Proxying image request to: ${imageUrl}`);

        // 添加 Authorization 头
        const fetchOptions = POLLINATIONS_API_KEY ? {
            headers: {
                'Authorization': `Bearer ${POLLINATIONS_API_KEY}`
            }
        } : {};

        // Fetch the image directly from Pollinations.AI
        const response = await fetch(imageUrl, fetchOptions);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`Error from Pollinations.AI: ${response.status} - ${errorText}`);
            return res.status(response.status).send(`Error from Pollinations.AI: ${errorText}`);
        }

        // Set appropriate headers and pipe the image stream back to the client
        res.setHeader('Content-Type', response.headers.get('Content-Type') || 'image/jpeg');
        response.body.pipe(res);

    } catch (error) {
        console.error('Error generating image via backend:', error);
        res.status(500).json({ error: `Failed to generate image: ${error.message}` });
    }
});

app.listen(port, () => {
    console.log(`Server listening at http://localhost:${port}`);
    console.log(`Open your browser to http://localhost:${port}/index.html`);
});