<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pollinations Image Generator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Pollinations 图像生成器</h1>

        <div class="input-group">
            <label for="prompt">图像描述:</label>
            <input type="text" id="prompt" placeholder="输入你想生成的图像描述，例如: A futuristic city skyline at sunset">
        </div>

        <div class="flex-container" style="display: flex; flex-wrap: wrap; justify-content: space-between; gap: 15px; margin-bottom: 15px;">
            <div class="input-group" style="flex: 1; min-width: 200px; margin-bottom: 0;">
                <label for="model">选择模型:</label>
                <select id="model">
                    <option value="" disabled>选择一个模型</option>
                    <option value="https://models.pollinations.ai/flux" selected>flux</option>
                    <option value="https://models.pollinations.ai/turbo">turbo</option>
                    <option value="https://models.pollinations.ai/gptimage">gptimage</option>
                </select>
            </div>

            <div class="input-group" style="flex: 1; min-width: 200px; margin-bottom: 0;">
                <label for="seed">种子 (Seed):</label>
                <input type="number" id="seed" placeholder="可选，用于复现结果">
            </div>

            <div class="input-group" style="flex: 1; min-width: 200px; margin-bottom: 0;">
                <label for="width">宽度 (Width):</label>
                <input type="number" id="width" value="1024" min="1">
            </div>

            <div class="input-group" style="flex: 1; min-width: 200px; margin-bottom: 0;">
                <label for="height">高度 (Height):</label>
                <input type="number" id="height" value="1024" min="1">
            </div>
        </div>

        <div class="checkboxes-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
            <div class="checkbox-group">
                <input type="checkbox" id="nsfw-checkbox">
                <label for="nsfw-checkbox">允许NSFW内容 (实验性)</label>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="nologo-checkbox" checked>
                <label for="nologo-checkbox">禁用水印 (No Logo)</label>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="private-checkbox" checked>
                <label for="private-checkbox">私有图片 (Private)</label>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="enhance-checkbox" checked>
                <label for="enhance-checkbox">增强描述 (Enhance Prompt)</label>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="safe-checkbox">
                <label for="safe-checkbox">安全过滤 (Safe)</label>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="transparent-checkbox">
                <label for="transparent-checkbox">透明背景 (Transparent) (仅限gptimage模型)</label>
            </div>
        </div>

        <button id="generateBtn">生成图像</button>

        <div id="status"></div>
        <div id="error"></div>

        <div id="imageContainer" style="display: none;">
            <img id="generatedImage" class="hidden" alt="生成的图像">
        </div>
    </div>

    <div class="history-container">
        <h2>历史记录</h2>
        <div class="history-grid" id="historyGrid"></div>
        <button id="clearHistoryBtn" class="clear-history-btn">清空历史</button>
    </div>

    <!-- The Modal -->
    <div id="imageModal" class="modal">
        <span class="close">&times;</span>
        <img class="modal-content" id="img01">
        <div id="caption"></div>
    </div>

    <script>
        const promptInput = document.getElementById('prompt');
        const generateButton = document.getElementById('generateBtn');
        const statusDiv = document.getElementById('status');
        const errorDiv = document.getElementById('error');
        const imageContainer = document.getElementById('imageContainer');
        const generatedImage = document.getElementById('generatedImage');
        const clearHistoryBtn = document.getElementById('clearHistoryBtn');
        const historyGrid = document.getElementById('historyGrid');

        // 新增的功能选项变量
        const modelSelect = document.getElementById('model');
        const seedInput = document.getElementById('seed');
        const widthInput = document.getElementById('width');
        const heightInput = document.getElementById('height');
        const nologoCheckbox = document.getElementById('nologo-checkbox');
        const privateCheckbox = document.getElementById('private-checkbox');
        const enhanceCheckbox = document.getElementById('enhance-checkbox');
        const safeCheckbox = document.getElementById('safe-checkbox');
        const transparentCheckbox = document.getElementById('transparent-checkbox');
        const nsfwCheckbox = document.getElementById('nsfw-checkbox');


        // IndexedDB setup
        const DB_NAME = 'pollinations_db';
        const STORE_NAME = 'image_history';
        let db;

        function openDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(DB_NAME, 1);

                request.onupgradeneeded = function(event) {
                    db = event.target.result;
                    if (!db.objectStoreNames.contains(STORE_NAME)) {
                        const objectStore = db.createObjectStore(STORE_NAME, { keyPath: 'id', autoIncrement: true });
                        objectStore.createIndex('timestamp', 'timestamp', { unique: false });
                    }
                };

                request.onsuccess = function(event) {
                    db = event.target.result;
                    console.log('IndexedDB opened successfully');
                    resolve(db);
                };

                request.onerror = function(event) {
                    console.error('IndexedDB error:', event.target.errorCode);
                    reject(event.target.errorCode);
                };
            });
        }

        async function addImageToHistoryDB(prompt, blob, isError = false, id = null) {
            const historyItem = { prompt, timestamp: new Date().toISOString(), isError };
            if (blob) {
                historyItem.imageBlob = blob;
            }

            return new Promise(async (resolve, reject) => {
                if (!db) {
                    await openDB();
                }
                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const objectStore = transaction.objectStore(STORE_NAME);

                if (id !== null) {
                    // Update existing item
                    const request = objectStore.get(id);
                    request.onsuccess = function(event) {
                        const data = event.target.result;
                        if (data) {
                            Object.assign(data, historyItem);
                            const updateRequest = objectStore.put(data);
                            updateRequest.onsuccess = () => resolve(data.id);
                            updateRequest.onerror = (e) => reject(e);
                        } else {
                            reject('Item not found for update');
                        }
                    };
                    request.onerror = (e) => reject(e);
                } else {
                    // Add new item
                    const request = objectStore.add(historyItem);
                    request.onsuccess = () => resolve(request.result); // Resolve with the new ID
                    request.onerror = (e) => reject(e);
                }
            });
        }

        async function getHistoryFromDB() {
            return new Promise(async (resolve, reject) => {
                if (!db) {
                    await openDB();
                }
                const transaction = db.transaction([STORE_NAME], 'readonly');
                const objectStore = transaction.objectStore(STORE_NAME);
                const request = objectStore.getAll();

                request.onsuccess = function(event) {
                    resolve(event.target.result.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)));
                };

                request.onerror = function(event) {
                    console.error('Error fetching history:', event.target.errorCode);
                    reject(event.target.errorCode);
                };
            });
        }

        async function clearHistoryDB() {
            return new Promise(async (resolve, reject) => {
                if (!db) {
                    await openDB();
                }
                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const objectStore = transaction.objectStore(STORE_NAME);
                const request = objectStore.clear();

                request.onsuccess = function() {
                    console.log('History cleared from IndexedDB');
                    resolve();
                };

                request.onerror = function(event) {
                    console.error('Error clearing history:', event.target.errorCode);
                    reject(event.target.errorCode);
                };
            });
        }

        function displayHistoryItem(item) {
            const historyItemDiv = document.createElement('div');
            historyItemDiv.className = 'history-item';
            historyItemDiv.dataset.id = item.id; // Store ID for updates

            if (item.isError) {
                historyItemDiv.classList.add('error');
                historyItemDiv.innerHTML = `<span class="error-message">生成失败</span><br><span class="prompt-text-loading">${item.prompt}</span>`;
            } else if (item.imageBlob) {
                const imageUrl = URL.createObjectURL(item.imageBlob);
                historyItemDiv.innerHTML = `<img class="history-thumb" src="${imageUrl}" alt="${item.prompt}"><div class="prompt-preview">${item.prompt}</div><div class="prompt-overlay">${item.prompt}</div>`;
                // 只允许点击图片本身才弹窗
                const thumb = historyItemDiv.querySelector('.history-thumb');
                if (thumb) {
                    thumb.addEventListener('click', (event) => {
                        event.stopPropagation();
                        openImageModal(imageUrl, item.prompt);
                    });
                }
            } else {
                historyItemDiv.classList.add('loading');
                historyItemDiv.innerHTML = `<div class="loading-spinner">生成中...</div><div class="prompt-text-loading">${item.prompt}</div>`;
            }
            historyGrid.prepend(historyItemDiv); // Add to the beginning of the grid
            return historyItemDiv; // Return the created element
        }

        async function loadHistory() {
            const history = await getHistoryFromDB();
            historyGrid.innerHTML = ''; // Clear existing
            history.forEach(displayHistoryItem);
            // 优化：移除所有旧的事件监听，防止重复绑定
            // 这里不需要为historyGrid整体绑定事件，只需要每个history-item绑定
        }

        // Image Modal functions
        const modal = document.getElementById('imageModal');
        const modalImg = document.getElementById('img01');
        const captionText = document.getElementById('caption');
        const span = document.getElementsByClassName('close')[0];

        // Ensure modal is hidden on load
        modal.style.display = 'none';
        modal.classList.remove('active');

        function openImageModal(imageUrl, prompt) {
            modal.style.display = 'flex';
            modal.classList.add('active');
            modalImg.src = imageUrl;
            captionText.innerHTML = prompt;
        }

        span.onclick = function() {
            modal.style.display = 'none';
            modal.classList.remove('active');
        }

        modal.onclick = function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
                modal.classList.remove('active');
            }
        }

        // Initial load
        openDB().then(loadHistory);

        clearHistoryBtn.addEventListener('click', async () => {
            if (confirm('确定要清除所有历史记录吗？')) {
                await clearHistoryDB();
                loadHistory();
            }
        });

        generateButton.addEventListener('click', async () => {
            const prompt = promptInput.value;
            const model = modelSelect.value; // 获取模型选择的值
            const seed = seedInput.value; // 获取种子值
            const width = widthInput.value; // 获取宽度值
            const height = heightInput.value; // 获取高度值
            const nologo = nologoCheckbox.checked; // 获取禁用水印状态
            const isPrivate = privateCheckbox.checked; // 获取私有图片状态
            const enhance = enhanceCheckbox.checked; // 获取增强描述状态
            const safe = safeCheckbox.checked; // 获取安全过滤状态
            const transparent = transparentCheckbox.checked; // 获取透明背景状态
            const nsfw = nsfwCheckbox.checked; // 获取NSFW状态

            if (!prompt) {
                alert('请输入图像描述！');
                return;
            }
            if (!model) {
                alert('请选择一个模型！');
                return;
            }

            statusDiv.textContent = '正在发送请求...';
            errorDiv.textContent = '';
            generateButton.disabled = true;

            console.log('点击生成按钮，开始处理...');

            // Step 1: Create a temporary loading item for display immediately
            const tempLoadingItemData = { prompt, isError: false, imageBlob: null };
            const loadingItemDiv = displayHistoryItem(tempLoadingItemData); // This *creates* and *prepends* the div.

            // Step 2: Add to IndexedDB to get the actual ID
            let actualHistoryItemId = null;
            try {
                console.log('尝试添加到 IndexedDB...');
                actualHistoryItemId = await addImageToHistoryDB(prompt, null, false);
                loadingItemDiv.dataset.id = actualHistoryItemId; // Update the DOM element with the real ID
                console.log('IndexedDB 添加成功，ID:', actualHistoryItemId);
            } catch (dbError) {
                console.error('Error adding loading item to DB:', dbError);
                // Handle DB error, e.g., mark the DOM item as error
                loadingItemDiv.classList.add('error');
                loadingItemDiv.innerHTML = `<span class="error-message">DB错误</span><br><span class="prompt-text-loading">${prompt}</span>`;
                statusDiv.textContent = '';
                errorDiv.textContent = `DB操作失败: ${dbError.message}`;
                generateButton.disabled = false;
                return; // Stop further processing if DB fails for loading state
            }

            console.log('准备发送图像生成请求...');
            try {
                const response = await fetch('/generate-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        model: model,
                        seed: seed ? parseInt(seed) : undefined, // 转换为数字或 undefined
                        width: width ? parseInt(width) : undefined,
                        height: height ? parseInt(height) : undefined,
                        nologo: nologo,
                        isPrivate: isPrivate,
                        enhance: enhance,
                        safe: safe,
                        transparent: transparent,
                        nsfw: nsfw
                    }),
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(errorText || '图片生成失败');
                }

                const blob = await response.blob();
                const imageUrl = URL.createObjectURL(blob);

                // Update the loading item with the generated image
                await addImageToHistoryDB(prompt, blob, false, actualHistoryItemId); 
                
                // Replace the loading item with the actual image item's content
                loadingItemDiv.classList.remove('loading');
                loadingItemDiv.innerHTML = `<img src="${imageUrl}" alt="${prompt}"><div class="prompt-preview">${prompt}</div><div class="prompt-overlay">${prompt}</div>`;
                loadingItemDiv.addEventListener('click', (event) => {
                    event.stopPropagation();
                    openImageModal(imageUrl, prompt);
                });

            } catch (error) {
                console.error('Error generating image:', error);
                statusDiv.textContent = '';
                errorDiv.textContent = `生成失败: ${error.message}`;

                // Update the loading item to an error state
                await addImageToHistoryDB(prompt, null, true, actualHistoryItemId); 
                loadingItemDiv.classList.remove('loading');
                loadingItemDiv.classList.add('error');
                loadingItemDiv.innerHTML = `<span class="error-message">生成失败</span><br><span class="prompt-text-loading">${prompt}</span>`;

            } finally {
                generateButton.disabled = false;
            }
        });
    </script>
</body>
</html>
